

Examples using ``pyCATHY.DA``
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^


.. start-sphx-glr-thumbnails


.. raw:: html

    <div class="sphx-glr-thumbnails">

.. thumbnail-parent-div-open

.. raw:: html

    <div class="sphx-glr-thumbcontainer" tooltip="This notebook demonstrates how to create an ensemble of models with varying initial conditions for different soil layers. By introducing randomness into the initial conditions, we can better simulate the natural variability in soil properties and improve the robustness of data assimilation (DA) processes.">

.. only:: html

  .. image:: /content/DA/images/thumb/sphx_glr_plot_1_DA_NoUni_ic_thumb.png
    :alt:

  :ref:`sphx_glr_content_DA_plot_1_DA_NoUni_ic.py`

.. raw:: html

      <div class="sphx-glr-thumbnail-title">DA with Random Initial Conditions on Soil Layers</div>
    </div>


.. only:: not html

 * :ref:`sphx_glr_content_DA_plot_1_DA_NoUni_ic.py`

.. thumbnail-parent-div-close

.. raw:: html

    </div>

