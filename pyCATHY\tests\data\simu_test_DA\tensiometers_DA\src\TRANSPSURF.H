C
C**************************  COMMON BLOCK TRANSPSURF *******************
C            
C  real scalars and real arrays for surface transport routing
C            
C***********************************************************************

      REAL*8 AK_MAX_TRA,AK_MAX_TRA_SAV,AK_MAX_TRA_P,CUTRGT_TRA
      REAL*8 CU_MAX_TRA,CONC_KKP1_SN(MAXCEL)
      REAL*8 SURFACE_CONC_SN(MAXCEL)
      REAL*8 QMASS_IN_KK_SN(MAXCEL),QMASS_IN_KK_SN_SAV(MAXCEL)
      REAL*8 QMASS_IN_KK_SN_P(MAXCEL),QMASS_IN_KKP1_SN(MAXCEL)
      REAL*8 QMASS_OUT_KK_SN_1(MAXCEL),QMASS_OUT_KK_SN_1_SAV(MAXCEL)
      REAL*8 QMASS_OUT_KK_SN_2(MAXCEL),QMASS_OUT_KK_SN_2_SAV(MAXCEL)
      REAL*8 QMASS_OUT_KK_SN_1_P(MAXCEL),QMASS_OUT_KKP1_SN_1(MAXCEL)
      REAL*8 QMASS_OUT_KK_SN_2_P(MAXCEL),QMASS_OUT_KKP1_SN_2(MAXCEL)
      REAL*8 MASS_KK_SN(MAXCEL),MASS_KK_SN_SAV(MAXCEL)
      REAL*8 MASS_KK_SN_P(MAXCEL),MASS_KKP1_SN(MAXCEL)

      COMMON /TRANSPSURF/
     A       AK_MAX_TRA,AK_MAX_TRA_SAV,AK_MAX_TRA_P,CUTRGT_TRA,
     D       CU_MAX_TRA,CONC_KKP1_SN,SURFACE_CONC_SN,
     E       QMASS_IN_KK_SN,QMASS_IN_KK_SN_SAV,
     F       QMASS_IN_KK_SN_P,QMASS_IN_KKP1_SN,
     G       QMASS_OUT_KK_SN_1,QMASS_OUT_KK_SN_1_SAV,
     H       QMASS_OUT_KK_SN_2,QMASS_OUT_KK_SN_2_SAV,
     I       QMASS_OUT_KK_SN_1_P,QMASS_OUT_KK_SN_2_P,
     L       QMASS_OUT_KKP1_SN_1,QMASS_OUT_KKP1_SN_2,
     M       MASS_KK_SN,MASS_KK_SN_SAV,
     N       MASS_KK_SN_P,MASS_KKP1_SN
