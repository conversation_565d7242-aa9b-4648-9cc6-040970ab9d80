C
C**************************  TIME_INTERPOLATION ************************
C
C  INTERPOLATE VARIABLES TO BE USED BY VOLFIN AT EACH ADVECTIVE TIME
C  STEP
C  VARIABLES ARE TAKEN AT EACH ADVECTIVE Ka+1 TIMES WHILE 
C  FLUXES AND VELOCITIES AT K+1 
C
C***********************************************************************
C
      SUBROUTINE TIME_INTERPOLATION(N,NT,NFACE,NNOD,TADV,NADV,
     1           VELREC,SWINT_EL,SWP_EL,SW_EL,
     2           UUINT,VVINT,WWINT,UUOLD,VVOLD,WWOLD,UU,VV,WW,
     3           ATMINT,ATMOLD,ATMACT,FACEFLUX,FACES_FACEFLUXOLD,
     4           FACES_FACEFLUX,B<PERSON><PERSON>LOWINT_NODE,<PERSON><PERSON><PERSON><PERSON>OWP_NODE,
     5           BKCFLOW_NODE)
C      
      IMPLICIT NONE
C     LOCAL 
      INTEGER I,J
C     PASSED      
      INTEGER N,NT,NFACE,NNOD,TADV,NADV
      INTEGER VELREC 
C     PASSED/PASSED AND UPDATED      
      REAL*8  SWINT_EL(*),SWP_EL(*),SW_EL(*)  
      REAL*8  UUINT(*),UUOLD(*),UU(*)
      REAL*8  VVINT(*),VVOLD(*),VV(*)
      REAL*8  WWINT(*),WWOLD(*),WW(*)
      REAL*8  ATMINT(*),ATMOLD(*),ATMACT(*)
      REAL*8  FACEFLUX(3,*),FACES_FACEFLUXOLD(3,*),FACES_FACEFLUX(3,*)
      REAL*8  BKCFLOW_NODE(*),BKCFLOWP_NODE(*),BKCFLOWINT_NODE(*)
C
c INTERPOLATE SATURATION
      DO I=1,NT
          SWINT_EL(I)=SWP_EL(I)+((FLOAT(TADV)/FLOAT(NADV))
     1    *(SW_EL(I)-SWP_EL(I)))
      END DO
c     write(*,*) SWINT_EL(16453),SW_EL(16453)
C INTERPOLATE ELEMENTAL VELOCITIES
      DO I=1,NT
         UUINT(I)=UU(I)
         VVINT(I)=VV(I)
         WWINT(I)=WW(I)
      END DO
C INTERPOLATION OF ACTUAL ATMOSPHERIC VALUES
      DO I=1,NNOD
         ATMINT(I)=ATMACT(I)
      END DO
      DO I=1,N
         BKCFLOWINT_NODE(I)=BKCFLOW_NODE(I)
      END DO 
C INTERPOLATION OF FACES FLUX FOR VELOCITY RECONSTRUCTION
      IF(VELREC.EQ.1)THEN        
         DO I=1,NFACE
            DO J=1,3
               FACEFLUX(J,I)=Faces_FaceFlux(J,I)
            END DO   
         END DO         
      END IF

      RETURN
      END
