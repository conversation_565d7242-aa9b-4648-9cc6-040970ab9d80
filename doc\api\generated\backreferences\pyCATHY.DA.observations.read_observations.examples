

Examples using ``pyCATHY.DA.observations.read_observations``
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^


.. start-sphx-glr-thumbnails


.. raw:: html

    <div class="sphx-glr-thumbnails">

.. thumbnail-parent-div-open

.. raw:: html

    <div class="sphx-glr-thumbcontainer" tooltip="The notebook illustrate how to read SMC sensors dataset to be prepare for DA">

.. only:: html

  .. image:: /content/DA/images/thumb/sphx_glr_plot_2_prepare_SMC_obs4DA_thumb.png
    :alt:

  :ref:`sphx_glr_content_DA_plot_2_prepare_SMC_obs4DA.py`

.. raw:: html

      <div class="sphx-glr-thumbnail-title">Read SMC sensors observations to assimilate</div>
    </div>


.. only:: not html

 * :ref:`sphx_glr_content_DA_plot_2_prepare_SMC_obs4DA.py`

.. thumbnail-parent-div-close

.. raw:: html

    </div>

