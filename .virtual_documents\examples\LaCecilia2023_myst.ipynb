




















import os
import matplotlib.pyplot as plt
import numpy as np
from pyCATHY import cathy_tools
from pyCATHY.importers import cathy_inputs as in_CT
from pyCATHY.importers import cathy_outputs as out_CT
from pyCATHY.plotters import cathy_plots as cplt

import matplotlib as mpl
# set some default plotting parameters for nicer looking plots
mpl.rcParams.update({"axes.grid":True, "grid.color":"gray", "grid.linestyle":'--','figure.figsize':(10,10)})





# Initiate a CATHY model
# ------------------------
path2prj = '.'  # add your local path here
simu = cathy_tools.CATHY(dirName=path2prj, prj_name="LaCecilia_2022_HP")











DEM, header_DEM = simu.read_inputs('dem')


fig, ax = plt.subplots(1)
img = ax.imshow(DEM,vmin=0,vmax=500)
plt.colorbar(img)
simu.show_input(prop="dem",vmin=0,vmax=500)





VEG_MAP, header_veg_map = simu.read_inputs('root_map')
VEG_MAP


fig, ax = plt.subplots(1)
simu.show_input(prop="root_map", ax=ax,
               linewidth=0)









SPP, FP = simu.read_inputs('soil', MAXVEG=8)
FP





simu.update_veg_map()
simu.update_soil(FP_map=FP, show=True)





# first we read and plot the current atmbc file 
# --------------------------------
df_atmbc = simu.read_inputs('atmbc')
simu.show_input('atmbc')








# first we read the atmbc file 
# --------------------------------
atmbc_hourlyf = os.path.join(simu.workdir,simu.project_name,'input','atmbc_hourly')
df_atmbc_hourly, HSPATM, IETO = in_CT.read_atmbc(atmbc_hourlyf)


help(simu.update_atmbc)
df_atmbc_hourly = df_atmbc_hourly.iloc[0:-2]


simu.update_atmbc(
                    HSPATM=1,
                    IETO=0,
                    time=list(df_atmbc_hourly['time']),
                    netValue=list(df_atmbc_hourly['value']),
)
simu.show_input('atmbc')





# first we read the current soil file 
# --------------------------------
simu.update_veg_map()
df_soil_het, df_FP = simu.read_inputs('soil')

# show Feddes parameters table
# --------------------------------
df_FP








df_soil, df_FP = simu.read_inputs('soil')
df_soil


layers2plot= [1,6,9]

fig, axs = plt.subplots(1,3, 
                        sharex=True, 
                        sharey=True
                       )
axs= axs.ravel()
for i, ax in enumerate(axs):
    simu.show_input(
                    prop="soil", 
                    yprop="PERMX", 
                    layer_nb=layers2plot[i],
                    ax = ax,
                    linewidth=0,
                   )
    ax.axis('square')
    ax.set_title('layer'+ str(i+1))






# define the path to the soil file
soilfile_het = os.path.join(simu.workdir,simu.project_name,'input','soil_het')

# define the path to the dem_parameter file
dem_parmfile = os.path.join(simu.workdir,simu.project_name,'input','dem_parameters')

# define the path to the dem_parameter file
dempar = in_CT.read_dem_parameters(dem_parmfile)

# read soil heterogeneous file
df_soil_het, _ = in_CT.read_soil(soilfile_het,dempar,MAXVEG=8)
df_soil_het


simu.update_soil(SPP=df_soil_het)


layers2plot= [1,6,9]

fig, axs = plt.subplots(1,3, 
                        sharex=True, 
                        sharey=True
                       )
axs= axs.ravel()
for i, ax in enumerate(axs):
    simu.show_input(
                    prop="soil", 
                    yprop="PERMX", 
                    layer_nb=layers2plot[i],
                    ax = ax,
                    linewidth=0,
                   )
    ax.axis('square')
    ax.set_title('layer'+ str(i+1))








#simu.read_inputs('parm')
parm_file = os.path.join(simu.workdir,simu.project_name,'input','parm')
parm = in_CT.read_parm(parm_file)











#%% Another interesting graph looking at the **streamflow = f(time)**
simu.show(prop="hgraph")





fig, ax = plt.subplots(1)
simu.show('spatialET',ax=ax, ti=10)





fig, axs = plt.subplots(1,2)
fig.subplots_adjust(right=0.8)
cbar_ax = fig.add_axes([0.85, 0.15, 0.05, 0.7])

cmap = simu.show('WTD',ax=axs[0], ti=9, colorbar=False)
cmap = simu.show('WTD',ax=axs[1], ti=10, colorbar=False)

plt.colorbar(cmap,cax=cbar_ax)





simu.show('WTD',ti=[1,10])
