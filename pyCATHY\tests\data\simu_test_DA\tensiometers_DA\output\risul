
     ISIMGR (0 FLOW3D only w/ grid input, 
             1 FLOW3D only w/ DEM input, 
             2 FLOW3D and SURF_ROUTE w/ DEM) =      2
     PONDH_MIN (MIN. PONDING HEAD)           =     0.00000E+00
     IPRT1  (FOR OUTPUT OF INPUT DATA)       =      3
     IPRT   (FOR ELEM VEL & DET NODAL OUTPUT)=      4
     NPRT   (# OF TIME VALUES FOR DET OUTPUT)=      3
     NUMVP  (# OF SURF NODES FOR VP OUTPUT)  =      1
     NR     (# OF NODES FOR PARTIAL OUTPUT)  =      0

     KSLOPE (0 ANA, 1 KSL/ANA, 2 KSL/C-DIFF,
             3 LOC KSL/ANA, 4 LOC TAN-SLOPE) =      0
     TOLKSL (TOLERANCE FOR CHORD SLOPE)      =     1.00000E-02

     ISFONE (0 ALL-NODE SFEX UPD, 1 1-NODE)  =      0
     ISFCVG (0 NL CONVG W/O SFEX, 1 W/ SFEX) =      0
     TETAF  (EG: 1 BACKWARD EULER, 0.5 C-N)  =     1.00000E+00
     LUMP   (MASS LUMPING IF NONZERO)        =      1
     IOPT   (1 PICARD, 2 NEWTON)             =      1
     NLRELX (0 NORELX,1 CONS RELX,2 VAR RELX)=      0
     L2NORM (0 INFINITY NORM, ELSE L2 NORM)  =      0
     TOLUNS (TOLERANCE FOR NONLINEAR ITER)   =     1.00000E-04
     TOLSWI (TOLERANCE FOR BC SWITCHING)     =     1.00000E+30
     ERNLMX (MAX ALLOWABLE CVG OR RESID ERR) =     1.00000E+30
     ITUNS  (MAX NONLINEAR ITER / TIME STEP) =     10
     ITUNS1 (DELTAT INCREASE THRESHOLD)      =      5
     ITUNS2 (DELTAT DECREASE THRESHOLD)      =      7

     ISOLV  (-5 BiCGSTAB w/ diag precond, 
             -4 BiCGSTAB without precond, 
             -3 TFQMR   w/ diag precond, 
             -2 TFQMR   without precond, 
             -1 TFQMR   w/ K^-1  precond, 
              0 BiCGSTAB w/ K^-1  precond, 
              1 GRAMRB (min residual), 
              2 GCRK(5) (ORTHOMIN), 
              3 NONSYM (direct solver))      =      2
     ITMXCG (MAX ITER FOR CG LINEAR SOLVERS) =    500
     TOLCG  (TOLER. FOR CG LINEAR SOLVERS)   =     1.00000E-10

     DELTAT (INITIAL TIME STEP SIZE)         =     1.00000E-02
     DTMIN  (MINIMUM TIME STEP SIZE)         =     1.00000E-05
     DTMAX  (MAXIMUM TIME STEP SIZE)         =     1.00000E+01
     TMAX   (TIME AT END OF SIMULATION)      =     1.00000E+00
     DTMAGA (MAG. FACTOR FOR DELTAT, ADD.)   =     0.00000E+00
     DTMAGM (MAG. FACTOR FOR DELTAT, MULT.)  =     1.10000E+00
     DTREDS (RED. FACTOR FOR DELTAT, SUB.)   =     0.00000E+00
     DTREDM (RED. FACTOR FOR DELTAT, MULT.)  =     5.00000E-01

     PMIN  (AIR DRY PRESSURE HEAD VALUE)     =    -5.00000E+00
     IPEAT (flag for peat deformation)       =      0
     IVGHU (-1 moisture curve lookup table, 
            0 van Genuchten, 
            1 extended van Genuchten, 
            2 Huyakorn with Kr=Se**n, 
            3 Huyakorn with log_10 Kr(Se), 
            4 Brooks-Corey)                  =      0
 SPATIALLY VARIABLE VAN GENUCHTEN PARAMETERS


     SATURATED HYDRAULIC CONDUCTIVITY, SPECIFIC STORAGE, AND POROSITY VALUES
  LAYER MAT.TYPE  X-PERM       Y-PERM       Z-PERM       STORAGE      POROSITY          VGN        VGRMC        VGPSAT
    1       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
    2       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
    3       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
    4       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
    5       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
    6       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
    7       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
    8       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
    9       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
   10       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
   11       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
   12       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
   13       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
   14       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
   15       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
   16       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
   17       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
   18       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02
   19       1    1.88000E-04  1.88000E-04  1.88000E-04  1.00000E-05  5.50000E-01  1.46000E+00  1.50000E-01  3.12500E-02

     N     (# OF NODES IN 3-D MESH)          =     2420
     NT    (# OF TETRAHEDRA IN 3-D MESH)     =    11400


 IPRT1=3: Program terminating after output of X, Y, Z coordinate values
